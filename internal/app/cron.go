package app

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

func StartReferralSnapshotCron(db *gorm.DB) {
	ticker := time.NewTicker(10 * time.Second)
	go func() {
		for {
			<-ticker.C
			updateAllReferralSnapshots(db)
		}
	}()
}

func updateAllReferralSnapshots(db *gorm.DB) {
	var userIDs []uuid.UUID
	db.Model(&model.User{}).Pluck("id", &userIDs)

	for _, userID := range userIDs {
		var directCount int64
		var totalDownlineCount int64

		// 统计直接下级（depth=1）
		db.Model(&model.Referral{}).
			Where("referrer_id = ? AND depth = 1", userID).
			Count(&directCount)

		// 统计所有下级
		db.Model(&model.Referral{}).
			Where("referrer_id = ? AND depth <= 3", userID).
			Count(&totalDownlineCount)

		// Calculate total transaction volume - Get the total transaction volume of all subordinate users
		var totalVolumeResult struct {
			TotalVolume decimal.Decimal
		}

		// Use subquery to get the transaction volume of all subordinate users
		err := db.Raw(`
			SELECT COALESCE(SUM(at.volume_usd), 0) as total_volume
			FROM affiliate_transactions at
			WHERE at.user_id IN (
				SELECT r.user_id
				FROM referrals r
				WHERE r.referrer_id = ? AND r.depth <= 3
			) AND at.status = ?
		`, userID, model.StatusCompleted).Scan(&totalVolumeResult).Error

		if err != nil {
			fmt.Printf("Error calculating total volume for user %s: %v\n", userID, err)
			totalVolumeResult.TotalVolume = decimal.Zero
		}

		// upsert ReferralSnapshot
		var snapshot model.ReferralSnapshot
		err = db.Where("user_id = ?", userID).First(&snapshot).Error
		if err == gorm.ErrRecordNotFound {
			snapshot = model.ReferralSnapshot{
				UserID:             userID,
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
				TotalVolumeUSD:     totalVolumeResult.TotalVolume,
			}
			db.Create(&snapshot)
		} else if err == nil {
			db.Model(&snapshot).Updates(model.ReferralSnapshot{
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
				TotalVolumeUSD:     totalVolumeResult.TotalVolume,
			})
		} else {
			fmt.Printf("Error updating snapshot for user %s: %v\n", userID, err)
		}
	}
	fmt.Println("ReferralSnapshot Statistics completed")
}
