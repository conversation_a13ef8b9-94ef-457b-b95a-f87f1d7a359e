// sidebars.internal.js
module.exports = {
    internalSidebar: [
        {
            type: 'category',
            label: '概述',
            items: ['index', 'user-onboarding/flow', 'user-onboarding/dev-steps', 'user-onboarding/roadmap', 'user-onboarding/internal-intro', 'user-onboarding/references'],
        },
        {
            type: 'category',
            label: '技术说明 & 规范',
            items: ['technical-docs/general', 'technical-docs/git-convention', 'technical-docs/config-env-policy'],
        },
        {
            type: 'category',
            label: 'HyperLiquid API 参考',
            items: ['hyperliquid-api/rest-api', 'hyperliquid-api/websocket-api'],
        },
        {
            type: 'category',
            label: '通用API模块',
            items: ['general-api/user-integration'],
        },
        {
            type: 'category',
            label: '系统后端服务模块',
            items: ['xbit-go-back/rango-integration', 'xbit-go-back/rango-worker-design', 'xbit-go-back/ohlcv', 'xbit-go-back/log-transaction'],
        },
        {
            type: 'category',
            label: '技术设计【Deprecated】',
            items: ['technical-design/telegram-login', 'technical-design/rust-rules', 'technical-design/rust-db', 'technical-design/rust-ds', 'technical-design/architecture', 'technical-design/system-flow'],
        },
        {
            type: 'category',
            label: 'HyperTrader【Deprecated】',
            items: ['hypertrader/hyperliquid-logic', 'hypertrader/signal-algorithm', 'hypertrader/signal-api', 'hypertrader/simulating-trace-api'],
        },
        {
            type: 'category',
            label: 'xbit-agent',
            items: ['xbit-agent/referral-tree'],
        },
    ],
};
