# log-transaction 下单交易日志集成文档

## log-transaction 集成流程图
![集成流程图](../../static/img/technical-design/log-transaction.png)

## API 接口说明

### 1. 生成 cloid (GenerateCloid)

#### GraphQL 查询

```graphql
query GenerateCloid {
  generateCloid(input: { count: 3 }) {
    cloids
    count
  }
}
```

#### 请求参数说明

- `count`: 请求生成 cloid 的数量，默认为一个（可选）

#### 响应

```json
{
  "data": {
    "generateCloid": {
      "count": 1,
      "cloids": ["0x98d554cd0c18c40cfc5890293e290760"],
      "__typename": "GenerateCloidResponse"
    }
  }
}
```

### 2. 保存下单交易日志

#### GraphQL 查询

```
mutation LogTransaction {
    logTransaction(input: [
            {
                baseCoin: "",
                side: buy,
                price: "",
                size: "",
                orderType: limit,
                operation: openOrder,
                orderStatus: resting,
                oid: "",
                cloid: "",
                avgPx: "",
                totalSz: "",
                grouping: normalTpsl,
                created: "",
                walletAddress: ""
            }
            ]) {
        error
        status
    }
}
```

#### 请求参数说明

- `baseCoin`: 币种
- `side`: 买入/卖出 buy/sell
- `price`: 价格
- `size`: 数量
- `orderType`: 订单类型 limit/market/tp_market/sl_market
- `operation`: openOrder/cancelOrder
- `orderStatus`: 订单状态 filled/resting/cancel
- `oid`: 订单 id
- `cloid`: 生成的 cloid
- `avgPx`: 平均价格
- `totalSz`: 总数量
- `grouping`: na/normalTpsl/positionTpsl
- `created`: 创建时间 timestamp 毫秒
- `walletAddress`: 主钱包

## nats 发送数据

#### 发送参数

```
{
  "items": [
    {
        "cloid": "0x1f9a3732091039e78cd0271081e14830",
        "user_id": "01979c65-c90c-7fdb-a93a-204ed19480ba",
        "wallet_address": "******************************************",
        "side": "Sell",
        "type": "Market",
        "size": "0.007",
        "price": "3522.2",
        "avg_price": "3829.3",
        "build_fee": "0.00246554",
        "fill_build_fee": "0.00246554"
        "status": "Filled",
        "oid": 122893650491,
        "created_at": "57545-04-16T19:36:35.000000000Z",
        "total_sz": "0.007",
        "hash": "0x92a35ef4dbf095919b8b042876e23102046400354214fcac975bcd7a73bf0912",
        "coin": "ETH",
        "reduce_only": false,
        "grouping": "Na",
        "operation": "openOrder"
    }
  ]
}
```
#### 参数说明
- `cloid`: 生成的cloid
- `user_id`: 用户id
- `wallet_address`: 钱包地址
- `side`: 买入/卖出
- `type`: 订单类型
- `size`: 数量
- `price`: 价格
- `avg_price`: 平均价格
- `build_fee`: 自己算的手续费
- `fill_build_fee`: hyperliquid算的手续费
- `status`: 订单状态
- `oid`: 订单id
- `created_at`: 创建时间
- `total_sz`: 总数量
- `hash`: 交易哈希
- `coin`: 币种
- `reduce_only` bool
- `grouping`: Na/NormalTpsl/PositionTpsl
- `operation`: openOrder/cancelOrder


## HyperLiquidVerifyOrder 表

#### 表参数说明
- `id`: 主键ID
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `order_created_at`: 订单创建时间
- `cloid`: 生成的唯一ID
- `user_id`: 用户id
- `user_address`: 钱包地址
- `side`: 买入/卖出
- `order_type`: 订单类型
- `symbol`: "BTC-USDT-PERP"
- `is_buy`: 是否买入
- `leverage`: 杠杆倍数
- `margin`: 保证金金额
- `is_market`: 是否为市价单
- `trigger_px`: 触发价格
- `tpsl`: 止盈止损
- `tif`: Time In Force: Alo/Ioc/Gtc
- `base`: "BTC"（基础代币）
- `quote`: "USDC"（计价代币）
- `size`: 合约数量
- `price`: 交易价格
- `avg_price`: 平均成交价格
- `build_fee`: 收取的手续费用
- `total_fee`: 总费用
- `fee_bp`: 费用基点
- `build_address`: 收取buildFee的地址
- `status`: 订单状态
- `oid`: 订单ID
- `total_sz`: 总成交数量
- `source_data`: 生成cloid的源数据
- `is_verified`: 是否验证
- `csv_verified`: 定时任务csv文件验证
- `expected_fee`: 期待的手续费
- `actual_fee`: 实际的手续费
- `fee_valid`: 是否有差异
- `affiliate_event_sent`: 是否已发送代理事件
- `hash`: 交易哈希
- `coin`: 币种
- `asset`: 资产
- `reduce_only`: bool
- `grouping`: na/normalTpsl/positionTpsl
- `operation`: openOrder/cancelOrder